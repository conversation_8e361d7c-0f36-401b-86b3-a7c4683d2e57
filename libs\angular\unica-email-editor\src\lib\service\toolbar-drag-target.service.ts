import { Injectable } from '@angular/core';
import { DragTargetProvider } from '../interface/drag-drop.interface';

/**
 * Default implementation of drag target provider for toolbar elements
 */
@Injectable()
export class ToolbarDragTargetService implements DragTargetProvider {
  
  /**
   * Configuration for drag target identification
   */
  private readonly targetConfig = {
    containerSelector: '.element-toolbar-container',
    draggingClass: 'dragging-toolbar'
  };

  /**
   * Get the drag target element (the toolbar container)
   */
  getDragTarget(sourceElement: HTMLElement): HTMLElement | null {
    return sourceElement.closest(this.targetConfig.containerSelector) as HTMLElement;
  }

  /**
   * Apply visual feedback to the drag target
   */
  applyDragFeedback(target: HTMLElement | null, isDragging: boolean): void {
    if (!target) return;

    if (isDragging) {
      // Apply dragging styles
      target.style.opacity = '0.4';
      target.style.transform = 'scale(0.95)';
      target.classList.add(this.targetConfig.draggingClass);
    } else {
      // Reset styles
      target.style.opacity = '1';
      target.style.transform = 'none';
      target.classList.remove(this.targetConfig.draggingClass);
    }
  }
}
