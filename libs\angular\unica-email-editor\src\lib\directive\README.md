# ToolbarDragDirective - Reactive Refactoring with RxJS & Renderer2

## Overview

The `ToolbarDragDirective` has been refactored to follow **separation of concerns** and **reactive programming** principles using RxJS streams and <PERSON><PERSON>'s Renderer2 for safe DOM manipulation.

## Problems Solved

### Before Refactoring
- **Hard-coded selectors**: Direct queries for `.drop-zone-left`, `.drop-zone-right`, `.element-toolbar-container`
- **Imperative event handling**: Manual event listener management with potential memory leaks
- **Direct DOM manipulation**: Unsafe direct style modifications
- **Tight coupling**: Directive was tightly coupled to specific HTML structure
- **No configurability**: Impossible to customize selectors without modifying the directive

### After Refactoring
- **Reactive streams**: RxJS-based event handling with automatic cleanup
- **Safe DOM manipulation**: All DOM operations use <PERSON><PERSON>'s Renderer2
- **Configurable selectors**: All external dependencies are configurable via inputs
- **Loose coupling**: Directive can work with any HTML structure by configuring selectors
- **Memory leak prevention**: Proper stream cleanup with takeUntil pattern

## Configuration Inputs

The directive now accepts these configuration inputs:

```typescript
// Drop zone selectors
@Input() dropZoneLeftSelector = '.drop-zone-left';
@Input() dropZoneRightSelector = '.drop-zone-right';

// Drag target selector
@Input() dragTargetSelector = '.element-toolbar-container';

// CSS classes
@Input() visibleClass = 'visible';
@Input() hoverClass = 'hover';

// Behavior configuration
@Input() dragThreshold = 10;
@Input() createDragImage = true;
```

## Usage

### Default Usage (No Changes Required)
The directive works exactly as before with default selectors:

```html
<div class="toolbar-drag-handle"
     toolbarDrag
     [toolbarDragEnabled]="true"
     [toolbarPosition]="position"
     (dragStateChange)="onDragStateChange($event)"
     (positionChange)="onPositionChange($event)">
</div>
```

### Custom Configuration
You can now customize the directive for different HTML structures:

```html
<div class="my-custom-drag-handle"
     toolbarDrag
     [toolbarDragEnabled]="true"
     [toolbarPosition]="position"
     [dropZoneLeftSelector]="'.my-left-zone'"
     [dropZoneRightSelector]="'.my-right-zone'"
     [dragTargetSelector]="'.my-container'"
     [visibleClass]="'active'"
     [hoverClass]="'highlighted'"
     [dragThreshold]="5"
     [createDragImage]="false"
     (dragStateChange)="onDragStateChange($event)"
     (positionChange)="onPositionChange($event)">
</div>
```

## Key Features

### RxJS Reactive Streams
- **Declarative event handling**: Mouse events are handled through reactive streams
- **Automatic cleanup**: `takeUntil(this.destroy$)` prevents memory leaks
- **Composable operations**: Drag logic is built using RxJS operators like `switchMap`, `filter`, `tap`

### Renderer2 Integration
- **Safe DOM manipulation**: All style changes use `renderer.setStyle()`
- **Class management**: CSS classes are managed with `renderer.addClass/removeClass()`
- **Element creation/removal**: DOM elements are safely created and removed
- **SSR compatibility**: Renderer2 ensures server-side rendering compatibility

### NgZone Integration
- **Change detection**: Events are emitted within `ngZone.run()` to trigger change detection
- **Performance**: DOM operations can run outside Angular's zone when needed

## Benefits

1. **Reactive Programming**: Leverages RxJS for clean, declarative event handling
2. **Memory Safety**: Proper stream cleanup prevents memory leaks
3. **DOM Safety**: Renderer2 ensures safe, SSR-compatible DOM manipulation
4. **Flexibility**: Easy to configure for different HTML structures
5. **Maintainability**: Clear separation between reactive logic and DOM operations
6. **Backward Compatibility**: Existing code continues to work unchanged
7. **Angular Best Practices**: Follows Angular's recommended patterns for directives

## Migration

### For Existing Code
No changes required - the directive maintains backward compatibility.

### For New Implementations
Simply configure the inputs to match your HTML structure and CSS classes.

## Example: Custom Drop Zones

If you have custom drop zones with different selectors:

```html
<!-- Your custom drop zones -->
<div class="custom-left-drop-area active"></div>
<div class="custom-right-drop-area active"></div>

<!-- Configure the directive -->
<div toolbarDrag
     [dropZoneLeftSelector]="'.custom-left-drop-area'"
     [dropZoneRightSelector]="'.custom-right-drop-area'"
     [visibleClass]="'active'">
</div>
```

This approach provides the separation of concerns benefits without the complexity of multiple services and interfaces.
