import { Injectable } from '@angular/core';
import { DropZoneDetector } from '../interface/drag-drop.interface';

/**
 * Default implementation of drop zone detection for toolbar positioning
 */
@Injectable()
export class ToolbarDropZoneService implements DropZoneDetector {
  
  /**
   * Configuration for drop zone selectors
   */
  private readonly dropZoneConfig = {
    left: '.drop-zone-left',
    right: '.drop-zone-right',
    visibleClass: 'visible',
    hoverClass: 'hover'
  };

  /**
   * Get the drop zone at the specified position
   */
  getDropZoneAtPosition(x: number, y: number): string | null {
    const leftDropZone = document.querySelector(this.dropZoneConfig.left) as HTMLElement;
    const rightDropZone = document.querySelector(this.dropZoneConfig.right) as HTMLElement;

    if (leftDropZone && this.isDropZoneVisible('left')) {
      const rect = leftDropZone.getBoundingClientRect();
      if (this.isPointInRect(x, y, rect)) {
        return 'left';
      }
    }

    if (rightDropZone && this.isDropZoneVisible('right')) {
      const rect = rightDropZone.getBoundingClientRect();
      if (this.isPointInRect(x, y, rect)) {
        return 'right';
      }
    }

    return null;
  }

  /**
   * Update visual feedback for drop zones
   */
  updateDropZoneHighlight(oldZone: string | null, newZone: string | null): void {
    // Remove highlight from old zone
    if (oldZone) {
      const oldElement = this.getDropZoneElement(oldZone);
      if (oldElement) {
        oldElement.classList.remove(this.dropZoneConfig.hoverClass);
      }
    }

    // Add highlight to new zone
    if (newZone) {
      const newElement = this.getDropZoneElement(newZone);
      if (newElement) {
        newElement.classList.add(this.dropZoneConfig.hoverClass);
      }
    }
  }

  /**
   * Check if a drop zone is currently visible/active
   */
  isDropZoneVisible(zoneId: string): boolean {
    const element = this.getDropZoneElement(zoneId);
    return element ? element.classList.contains(this.dropZoneConfig.visibleClass) : false;
  }

  /**
   * Get drop zone element by zone ID
   */
  private getDropZoneElement(zoneId: string): HTMLElement | null {
    const selector = zoneId === 'left' ? this.dropZoneConfig.left : this.dropZoneConfig.right;
    return document.querySelector(selector) as HTMLElement;
  }

  /**
   * Check if a point is within a rectangle
   */
  private isPointInRect(x: number, y: number, rect: DOMRect): boolean {
    return x >= rect.left && x <= rect.right && y >= rect.top && y <= rect.bottom;
  }
}
