/**
 * Interface for drop zone detection and management
 */
export interface DropZoneDetector {
  /**
   * Get the drop zone at the specified position
   * @param x - X coordinate
   * @param y - Y coordinate
   * @returns The drop zone identifier or null if no valid drop zone
   */
  getDropZoneAtPosition(x: number, y: number): string | null;

  /**
   * Update visual feedback for drop zones
   * @param oldZone - Previously highlighted zone
   * @param newZone - Currently highlighted zone
   */
  updateDropZoneHighlight(oldZone: string | null, newZone: string | null): void;

  /**
   * Check if a drop zone is currently visible/active
   * @param zoneId - The zone identifier
   * @returns True if the zone is visible and can accept drops
   */
  isDropZoneVisible(zoneId: string): boolean;
}

/**
 * Interface for drag target identification and management
 */
export interface DragTargetProvider {
  /**
   * Get the drag target element (the element that should be visually modified during drag)
   * @param sourceElement - The element that initiated the drag
   * @returns The target element or null if not found
   */
  getDragTarget(sourceElement: HTMLElement): HTMLElement | null;

  /**
   * Apply visual feedback to the drag target
   * @param target - The target element
   * @param isDragging - Whether dragging is active
   */
  applyDragFeedback(target: HTMLElement | null, isDragging: boolean): void;
}

/**
 * Configuration for drag behavior
 */
export interface DragConfig {
  /**
   * Minimum distance in pixels to move before starting drag
   */
  dragThreshold: number;

  /**
   * Whether to create a drag image that follows the cursor
   */
  createDragImage: boolean;

  /**
   * Custom styles for the drag image
   */
  dragImageStyles?: Partial<CSSStyleDeclaration>;
}

/**
 * Interface for drag image creation and management
 */
export interface DragImageProvider {
  /**
   * Create a drag image from the source element
   * @param sourceElement - The element to create an image from
   * @param config - Configuration for the drag image
   * @returns The created drag image element
   */
  createDragImage(sourceElement: HTMLElement, config: DragConfig): HTMLElement | null;

  /**
   * Update the position of the drag image
   * @param dragImage - The drag image element
   * @param x - X coordinate
   * @param y - Y coordinate
   */
  updateDragImagePosition(dragImage: HTMLElement, x: number, y: number): void;

  /**
   * Clean up the drag image
   * @param dragImage - The drag image element to remove
   */
  cleanupDragImage(dragImage: HTMLElement): void;
}

/**
 * Token for injecting drop zone detector
 */
export const DROP_ZONE_DETECTOR = 'DROP_ZONE_DETECTOR';

/**
 * Token for injecting drag target provider
 */
export const DRAG_TARGET_PROVIDER = 'DRAG_TARGET_PROVIDER';

/**
 * Token for injecting drag image provider
 */
export const DRAG_IMAGE_PROVIDER = 'DRAG_IMAGE_PROVIDER';

/**
 * Token for injecting drag configuration
 */
export const DRAG_CONFIG = 'DRAG_CONFIG';
