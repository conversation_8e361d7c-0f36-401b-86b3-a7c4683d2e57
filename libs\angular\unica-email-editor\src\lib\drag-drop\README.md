# Toolbar Drag & Drop Refactoring

## Overview

The `ToolbarDragDirective` has been refactored to follow **separation of concerns** principles by removing hard-coded dependencies on external DOM selectors and implementing a dependency injection-based architecture.

## Problems Solved

### Before Refactoring
- **Hard-coded selectors**: Direct queries for `.drop-zone-left`, `.drop-zone-right`, `.element-toolbar-container`
- **Tight coupling**: Directive was tightly coupled to specific HTML structure
- **Mixed responsibilities**: Single directive handled drag logic, drop zone detection, and visual feedback
- **No extensibility**: Impossible to customize behavior without modifying the directive

### After Refactoring
- **Abstracted interfaces**: Clear contracts for different responsibilities
- **Dependency injection**: Services are injected rather than hard-coded
- **Single responsibility**: Each service handles one specific concern
- **Extensible**: Easy to provide custom implementations

## Architecture

### Interfaces

#### `DropZoneDetector`
Handles drop zone detection and visual feedback:
```typescript
interface DropZoneDetector {
  getDropZoneAtPosition(x: number, y: number): string | null;
  updateDropZoneHighlight(oldZone: string | null, newZone: string | null): void;
  isDropZoneVisible(zoneId: string): boolean;
}
```

#### `DragTargetProvider`
Manages drag target identification and visual feedback:
```typescript
interface DragTargetProvider {
  getDragTarget(sourceElement: HTMLElement): HTMLElement | null;
  applyDragFeedback(target: HTMLElement | null, isDragging: boolean): void;
}
```

#### `DragImageProvider`
Handles drag image creation and management:
```typescript
interface DragImageProvider {
  createDragImage(sourceElement: HTMLElement, config: DragConfig): HTMLElement | null;
  updateDragImagePosition(dragImage: HTMLElement, x: number, y: number): void;
  cleanupDragImage(dragImage: HTMLElement): void;
}
```

### Default Implementations

1. **`ToolbarDropZoneService`**: Default implementation for toolbar drop zones
2. **`ToolbarDragTargetService`**: Default implementation for toolbar drag targets
3. **`ToolbarDragImageService`**: Default implementation for drag image creation

### Configuration

The `DragConfig` interface allows customization of drag behavior:
```typescript
interface DragConfig {
  dragThreshold: number;
  createDragImage: boolean;
  dragImageStyles?: Partial<CSSStyleDeclaration>;
}
```

## Usage

### Default Usage
The `EmailEditorElementToolbarComponent` automatically provides all necessary services:

```typescript
@Component({
  // ... other config
  providers: [
    { provide: DROP_ZONE_DETECTOR, useClass: ToolbarDropZoneService },
    { provide: DRAG_TARGET_PROVIDER, useClass: ToolbarDragTargetService },
    { provide: DRAG_IMAGE_PROVIDER, useClass: ToolbarDragImageService },
    { provide: DRAG_CONFIG, useValue: DEFAULT_TOOLBAR_DRAG_CONFIG }
  ]
})
```

### Custom Implementation
You can provide custom implementations for any service:

```typescript
@Component({
  providers: [
    { provide: DROP_ZONE_DETECTOR, useClass: MyCustomDropZoneService },
    { provide: DRAG_CONFIG, useValue: myCustomConfig }
  ]
})
export class MyCustomComponent { }
```

## Benefits

1. **Testability**: Each service can be mocked and tested independently
2. **Maintainability**: Clear separation of concerns makes code easier to understand
3. **Extensibility**: Easy to customize behavior without modifying core directive
4. **Reusability**: Services can be reused in other contexts
5. **Type Safety**: Strong typing through interfaces

## Migration Guide

### For Consumers
No changes required - the directive API remains the same.

### For Customization
Instead of modifying the directive, implement custom services:

1. Create custom service implementing the desired interface
2. Provide it in your component's providers array
3. The directive will automatically use your custom implementation

## Example

See `custom-drag-implementation.example.ts` for a complete example of custom implementations.
