import {
  Directive,
  ElementRef,
  EventEmitter,
  Input,
  Output,
  OnDestroy,
  Renderer2,
  NgZone,
  DestroyRef,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  fromEvent,
  Subject,
  takeUntil,
  switchMap,
  map,
  filter,
  tap,
} from 'rxjs';

@Directive({
  selector: '[toolbarDrag]',
  exportAs: 'toolbarDrag',
  standalone: true,
})
export class ToolbarDragDirective implements OnDestroy {
  /**
   * Whether the toolbar drag is enabled
   */
  @Input() toolbarDragEnabled = false;

  /**
   * Current position of the toolbar
   */
  @Input() toolbarPosition: 'left' | 'right' = 'right';

  /**
   * Configurable selectors for external dependencies
   */
  @Input() dropZoneLeftSelector = '.drop-zone-left';
  @Input() dropZoneRightSelector = '.drop-zone-right';
  @Input() dragTargetSelector = '.element-toolbar-container';
  @Input() visibleClass = 'visible';
  @Input() hoverClass = 'hover';

  /**
   * Drag behavior configuration
   */
  @Input() dragThreshold = 10;
  @Input() createDragImage = true;
  @Input() width = 46;

  /**
   * Event emitted when drag state changes
   */
  @Output() dragStateChange = new EventEmitter<boolean>();

  /**
   * Event emitted when position should change
   */
  @Output() positionChange = new EventEmitter<'left' | 'right'>();

  /**
   * RxJS subjects and observables
   */
  private dragStart$ = new Subject<MouseEvent>();
  private dragImage: HTMLElement | null = null;
  private currentDropZone: 'left' | 'right' | null = null;

  constructor(
    private elementRef: ElementRef<HTMLElement>,
    private renderer: Renderer2,
    private ngZone: NgZone,
    private destroyRef: DestroyRef
  ) {
    this.setupDragStreams();
  }

  /**
   * Setup reactive drag streams using RxJS
   */
  private setupDragStreams(): void {
    // Mouse down stream - starts the drag sequence
    const mouseDown$ = this.dragStart$.pipe(
      filter(() => this.toolbarDragEnabled),
      tap((event) => {
        event.preventDefault();
        event.stopPropagation();
        event.stopImmediatePropagation();
      }),
    );

    // Mouse move stream - tracks movement during drag
    const mouseMove$ = fromEvent<MouseEvent>(document, 'mousemove');
    const mouseUp$ = fromEvent<MouseEvent>(document, 'mouseup');

    // Drag sequence: mousedown -> mousemove until mouseup
    const drag$ = mouseDown$.pipe(
      switchMap((startEvent) => {
        const startX = startEvent.clientX;
        let isDragging = false;

        return mouseMove$.pipe(
          map((moveEvent) => ({
            startX,
            currentX: moveEvent.clientX,
            currentY: moveEvent.clientY,
            deltaX: moveEvent.clientX - startX,
            isDragging,
            moveEvent,
          })),
          tap((dragData) => {
            // Start dragging if threshold exceeded
            if (
              !dragData.isDragging &&
              Math.abs(dragData.deltaX) > this.dragThreshold
            ) {
              isDragging = true;
              dragData.isDragging = true;
              this.startDrag();
            }
          }),
          filter((dragData) => dragData.isDragging),
          tap((dragData) =>
            this.updateDragFeedback(dragData.currentX, dragData.currentY),
          ),
          takeUntil(mouseUp$.pipe(tap(() => this.endDrag()))),
        );
      }),
      takeUntilDestroyed(this.destroyRef),
    );

    // Subscribe to the drag stream
    drag$.subscribe();
  }

  /**
   * Handle mouse down on drag handle - trigger the reactive stream
   */
  onMouseDown(event: MouseEvent): void {
    this.dragStart$.next(event);
  }

  /**
   * Start the drag operation using Renderer2
   */
  private startDrag(): void {
    // Create drag image if enabled
    if (this.createDragImage) {
      this.createDragImageElement();
    }

    // Apply visual feedback to the drag target using Renderer2
    const dragTarget = this.getDragTarget();
    this.applyDragFeedback(dragTarget, true);

    // Notify parent that dragging started
    this.ngZone.run(() => {
      this.dragStateChange.emit(true);
    });
  }

  /**
   * Create a drag image that follows the cursor using Renderer2
   */
  private createDragImageElement(): void {
    const toolbarContainer = this.getDragTarget();
    if (!toolbarContainer) return;

    // Clone the toolbar container
    this.dragImage = toolbarContainer.cloneNode(true) as HTMLElement;

    // Style the drag image using Renderer2
    this.renderer.setStyle(this.dragImage, 'position', 'fixed');
    this.renderer.setStyle(this.dragImage, 'top', '50%');
    this.renderer.setStyle(this.dragImage, 'left', '50%');
    this.renderer.setStyle(
      this.dragImage,
      'transform',
      'translate(-50%, -5%) scale(1.05)',
    );
    this.renderer.setStyle(this.dragImage, 'opacity', '0.9');
    this.renderer.setStyle(this.dragImage, 'pointer-events', 'none');
    this.renderer.setStyle(this.dragImage, 'z-index', '99999');
    this.renderer.setStyle(
      this.dragImage,
      'background',
      'rgba(255, 255, 255, 0.95)',
    );
    this.renderer.setStyle(this.dragImage, 'border-radius', '8px');
    this.renderer.setStyle(
      this.dragImage,
      'box-shadow',
      '0 8px 32px rgba(0, 0, 0, 0.3)',
    );
    this.renderer.setStyle(this.dragImage, 'transition', 'none');
    this.renderer.setStyle(this.dragImage, 'height', 'auto');
    this.renderer.setStyle(this.dragImage, 'width', this.width + 'px');

    // Remove the filler element to make drag image cleaner (optional)
    const fillerElement = this.dragImage.querySelector('.filler');
    if (fillerElement) {
      this.renderer.removeChild(this.dragImage, fillerElement);
    }

    // If you need to modify the element container
    const elementContainer = this.dragImage.querySelector('.element-container');
    if (elementContainer) {
      // Example: Add a class to indicate this is a drag preview
      this.renderer.addClass(elementContainer, 'drag-preview-container');
    }

    // Add to document using Renderer2
    this.renderer.appendChild(document.body, this.dragImage);
  }

  /**
   * Get the drag target element
   */
  private getDragTarget(): HTMLElement | null {
    return this.elementRef.nativeElement.closest(
      this.dragTargetSelector,
    ) as HTMLElement;
  }

  /**
   * Apply visual feedback to the drag target using Renderer2
   */
  private applyDragFeedback(
    target: HTMLElement | null,
    isDragging: boolean,
  ): void {
    if (!target) return;

    if (isDragging) {
      this.renderer.setStyle(target, 'opacity', '0.4');
      this.renderer.setStyle(target, 'transform', 'scale(0.95)');
    } else {
      this.renderer.setStyle(target, 'opacity', '1');
      this.renderer.setStyle(target, 'transform', 'none');
    }
  }

  /**
   * Update drag feedback based on movement using Renderer2
   */
  private updateDragFeedback(mouseX: number, mouseY: number): void {
    // Update drag image position to follow cursor using Renderer2
    if (this.dragImage) {
      this.renderer.setStyle(this.dragImage, 'left', `${mouseX}px`);
      this.renderer.setStyle(this.dragImage, 'top', `${mouseY}px`);
      this.renderer.setStyle(
        this.dragImage,
        'transform',
        'translate(-50%, -5%) scale(1.05)',
      );
    }

    // Check which drop zone we're over
    const newDropZone = this.getDropZoneAtPosition(mouseX, mouseY);

    // Update visual feedback if drop zone changed
    if (newDropZone !== this.currentDropZone) {
      this.updateDropZoneHighlight(this.currentDropZone, newDropZone);
      this.currentDropZone = newDropZone;
    }
  }

  /**
   * Determine which drop zone is at the given position
   */
  private getDropZoneAtPosition(x: number, y: number): 'left' | 'right' | null {
    const leftDropZone = document.querySelector(
      this.dropZoneLeftSelector,
    ) as HTMLElement;
    const rightDropZone = document.querySelector(
      this.dropZoneRightSelector,
    ) as HTMLElement;

    if (leftDropZone && this.isElementVisible(leftDropZone)) {
      const rect = leftDropZone.getBoundingClientRect();
      if (this.isPointInRect(x, y, rect)) {
        return 'left';
      }
    }

    if (rightDropZone && this.isElementVisible(rightDropZone)) {
      const rect = rightDropZone.getBoundingClientRect();
      if (this.isPointInRect(x, y, rect)) {
        return 'right';
      }
    }

    return null;
  }

  /**
   * Check if an element is visible (has the visible class)
   */
  private isElementVisible(element: HTMLElement): boolean {
    return element.classList.contains(this.visibleClass);
  }

  /**
   * Check if a point is within a rectangle
   */
  private isPointInRect(x: number, y: number, rect: DOMRect): boolean {
    return (
      x >= rect.left && x <= rect.right && y >= rect.top && y <= rect.bottom
    );
  }

  /**
   * Update drop zone highlighting using Renderer2
   */
  private updateDropZoneHighlight(
    oldZone: 'left' | 'right' | null,
    newZone: 'left' | 'right' | null,
  ): void {
    // Remove highlight from old zone using Renderer2
    if (oldZone) {
      const oldElement = document.querySelector(
        oldZone === 'left'
          ? this.dropZoneLeftSelector
          : this.dropZoneRightSelector,
      ) as HTMLElement;
      if (oldElement) {
        this.renderer.removeClass(oldElement, this.hoverClass);
      }
    }

    // Add highlight to new zone using Renderer2
    if (newZone) {
      const newElement = document.querySelector(
        newZone === 'left'
          ? this.dropZoneLeftSelector
          : this.dropZoneRightSelector,
      ) as HTMLElement;
      if (newElement) {
        this.renderer.addClass(newElement, this.hoverClass);
      }
    }
  }

  /**
   * End the drag operation and determine new position using Renderer2
   */
  private endDrag(): void {
    // Clean up drag image using Renderer2
    if (this.dragImage && document.body.contains(this.dragImage)) {
      this.renderer.removeChild(document.body, this.dragImage);
      this.dragImage = null;
    }

    // Reset visual feedback
    const dragTarget = this.getDragTarget();
    this.applyDragFeedback(dragTarget, false);

    // Only change position if dropped on a valid drop zone
    if (this.currentDropZone && this.currentDropZone !== this.toolbarPosition) {
      const dropZone = this.currentDropZone;
      this.ngZone.run(() => {
        this.positionChange.emit(dropZone);
      });
    }

    // Reset drop zone tracking
    this.currentDropZone = null;

    // Notify parent that dragging ended
    this.ngZone.run(() => {
      this.dragStateChange.emit(false);
    });
  }

  /**
   * Cleanup on destroy - complete RxJS streams and clean up DOM
   */
  ngOnDestroy(): void {
    // Complete the drag start subject
    this.dragStart$.complete();

    // Clean up any remaining drag image using Renderer2
    if (this.dragImage && document.body.contains(this.dragImage)) {
      this.renderer.removeChild(document.body, this.dragImage);
    }
  }
}
