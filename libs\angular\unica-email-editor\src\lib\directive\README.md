# ToolbarDragDirective - Simplified Refactoring

## Overview

The `ToolbarDragDirective` has been refactored to follow **separation of concerns** principles by making external dependencies configurable through inputs instead of hard-coded selectors.

## Problems Solved

### Before Refactoring
- **Hard-coded selectors**: Direct queries for `.drop-zone-left`, `.drop-zone-right`, `.element-toolbar-container`
- **Tight coupling**: Directive was tightly coupled to specific HTML structure
- **No configurability**: Impossible to customize selectors without modifying the directive

### After Refactoring
- **Configurable selectors**: All external dependencies are configurable via inputs
- **Loose coupling**: Directive can work with any HTML structure by configuring selectors
- **Simple and maintainable**: No complex dependency injection or multiple services

## Configuration Inputs

The directive now accepts these configuration inputs:

```typescript
// Drop zone selectors
@Input() dropZoneLeftSelector = '.drop-zone-left';
@Input() dropZoneRightSelector = '.drop-zone-right';

// Drag target selector
@Input() dragTargetSelector = '.element-toolbar-container';

// CSS classes
@Input() visibleClass = 'visible';
@Input() hoverClass = 'hover';

// Behavior configuration
@Input() dragThreshold = 10;
@Input() createDragImage = true;
```

## Usage

### Default Usage (No Changes Required)
The directive works exactly as before with default selectors:

```html
<div class="toolbar-drag-handle"
     toolbarDrag
     [toolbarDragEnabled]="true"
     [toolbarPosition]="position"
     (dragStateChange)="onDragStateChange($event)"
     (positionChange)="onPositionChange($event)">
</div>
```

### Custom Configuration
You can now customize the directive for different HTML structures:

```html
<div class="my-custom-drag-handle"
     toolbarDrag
     [toolbarDragEnabled]="true"
     [toolbarPosition]="position"
     [dropZoneLeftSelector]="'.my-left-zone'"
     [dropZoneRightSelector]="'.my-right-zone'"
     [dragTargetSelector]="'.my-container'"
     [visibleClass]="'active'"
     [hoverClass]="'highlighted'"
     [dragThreshold]="5"
     [createDragImage]="false"
     (dragStateChange)="onDragStateChange($event)"
     (positionChange)="onPositionChange($event)">
</div>
```

## Benefits

1. **Simplicity**: Single directive file, no complex service architecture
2. **Flexibility**: Easy to configure for different HTML structures
3. **Maintainability**: Clear inputs make dependencies explicit
4. **Backward Compatibility**: Existing code continues to work unchanged
5. **No Over-Engineering**: Solves the problem without unnecessary complexity

## Migration

### For Existing Code
No changes required - the directive maintains backward compatibility.

### For New Implementations
Simply configure the inputs to match your HTML structure and CSS classes.

## Example: Custom Drop Zones

If you have custom drop zones with different selectors:

```html
<!-- Your custom drop zones -->
<div class="custom-left-drop-area active"></div>
<div class="custom-right-drop-area active"></div>

<!-- Configure the directive -->
<div toolbarDrag
     [dropZoneLeftSelector]="'.custom-left-drop-area'"
     [dropZoneRightSelector]="'.custom-right-drop-area'"
     [visibleClass]="'active'">
</div>
```

This approach provides the separation of concerns benefits without the complexity of multiple services and interfaces.
