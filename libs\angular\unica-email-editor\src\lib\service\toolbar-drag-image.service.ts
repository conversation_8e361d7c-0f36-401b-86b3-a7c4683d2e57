import { Injectable } from '@angular/core';
import { DragImageProvider, DragConfig } from '../interface/drag-drop.interface';

/**
 * Default implementation of drag image provider for toolbar elements
 */
@Injectable()
export class ToolbarDragImageService implements DragImageProvider {
  
  /**
   * Default drag image styles
   */
  private readonly defaultStyles: Partial<CSSStyleDeclaration> = {
    position: 'fixed',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -10%) scale(1.05)',
    opacity: '0.9',
    pointerEvents: 'none',
    zIndex: '99999',
    background: 'rgba(255, 255, 255, 0.95)',
    borderRadius: '8px',
    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',
    border: '2px solid var(--unica-primary, #038D99)',
    transition: 'none',
    height: 'auto'
  };

  /**
   * Create a drag image from the source element
   */
  createDragImage(sourceElement: HTMLElement, config: DragConfig): HTMLElement | null {
    if (!config.createDragImage) return null;

    // Find the toolbar container to clone
    const toolbarContainer = sourceElement.closest('.element-toolbar-container') as HTMLElement;
    if (!toolbarContainer) return null;

    // Clone the toolbar container
    const dragImage = toolbarContainer.cloneNode(true) as HTMLElement;

    // Apply styles
    const styles = { ...this.defaultStyles, ...config.dragImageStyles };
    Object.assign(dragImage.style, styles);

    // Remove drag handles from the clone to avoid confusion
    const dragHandles = dragImage.querySelectorAll('.toolbar-drag-handle');
    dragHandles.forEach((handle) => handle.remove());

    // Add to document
    document.body.appendChild(dragImage);

    return dragImage;
  }

  /**
   * Update the position of the drag image
   */
  updateDragImagePosition(dragImage: HTMLElement, x: number, y: number): void {
    dragImage.style.left = `${x}px`;
    dragImage.style.top = `${y}px`;
    dragImage.style.transform = 'translate(-50%, -10%) scale(1.05)';
  }

  /**
   * Clean up the drag image
   */
  cleanupDragImage(dragImage: HTMLElement): void {
    if (dragImage && document.body.contains(dragImage)) {
      document.body.removeChild(dragImage);
    }
  }
}
