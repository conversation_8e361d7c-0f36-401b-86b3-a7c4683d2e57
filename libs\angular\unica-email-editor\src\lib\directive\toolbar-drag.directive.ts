import {
  Directive,
  ElementRef,
  EventEmitter,
  Input,
  Output,
  OnDestroy,
  Inject,
  Optional,
} from '@angular/core';
import {
  DropZoneDetector,
  DragTargetProvider,
  DragImageProvider,
  DragConfig,
  DROP_ZONE_DETECTOR,
  DRAG_TARGET_PROVIDER,
  DRAG_IMAGE_PROVIDER,
  DRAG_CONFIG
} from '../interface/drag-drop.interface';

@Directive({
  selector: '[toolbarDrag]',
  exportAs: 'toolbarDrag',
  standalone: true,
})
export class ToolbarDragDirective implements OnDestroy {
  /**
   * Whether the toolbar drag is enabled
   */
  @Input() toolbarDragEnabled = false;

  /**
   * Current position of the toolbar
   */
  @Input() toolbarPosition: 'left' | 'right' = 'right';

  /**
   * Event emitted when drag state changes
   */
  @Output() dragStateChange = new EventEmitter<boolean>();

  /**
   * Event emitted when position should change
   */
  @Output() positionChange = new EventEmitter<'left' | 'right'>();

  /**
   * Mouse drag tracking
   */
  private isDragging = false;
  private startX = 0;
  private dragImage: HTMLElement | null = null;
  private currentDropZone: string | null = null;

  constructor(
    private elementRef: ElementRef<HTMLElement>,
    @Inject(DROP_ZONE_DETECTOR) private dropZoneDetector: DropZoneDetector,
    @Inject(DRAG_TARGET_PROVIDER) private dragTargetProvider: DragTargetProvider,
    @Inject(DRAG_IMAGE_PROVIDER) private dragImageProvider: DragImageProvider,
    @Inject(DRAG_CONFIG) private dragConfig: DragConfig
  ) {}

  /**
   * Handle mouse down on drag handle - start of reliable drag system
   */
  onMouseDown(event: MouseEvent): void {
    if (!this.toolbarDragEnabled) return;

    // Prevent CDK drag from interfering
    event.preventDefault();
    event.stopPropagation();
    event.stopImmediatePropagation();

    // Record starting position
    this.startX = event.clientX;
    this.isDragging = false;

    // Add global mouse event listeners
    const onMouseMove = (moveEvent: MouseEvent) => {
      const deltaX = moveEvent.clientX - this.startX;

      // Check if we've moved enough to start dragging
      if (!this.isDragging && Math.abs(deltaX) > this.dragConfig.dragThreshold) {
        this.startDrag();
      }

      if (this.isDragging) {
        // Update visual feedback and drag image position
        this.updateDragFeedback(deltaX, moveEvent.clientX, moveEvent.clientY);
      }
    };

    const onMouseUp = () => {
      if (this.isDragging) {
        this.endDrag();
      }

      // Clean up event listeners
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);
    };

    // Add global event listeners
    document.addEventListener('mousemove', onMouseMove);
    document.addEventListener('mouseup', onMouseUp);
  }

  /**
   * Start the drag operation
   */
  private startDrag(): void {
    this.isDragging = true;

    // Create drag image using the service
    this.dragImage = this.dragImageProvider.createDragImage(
      this.elementRef.nativeElement,
      this.dragConfig
    );

    // Apply visual feedback to the drag target using the service
    const dragTarget = this.dragTargetProvider.getDragTarget(this.elementRef.nativeElement);
    this.dragTargetProvider.applyDragFeedback(dragTarget, true);

    // Notify parent that dragging started
    this.dragStateChange.emit(true);
  }



  /**
   * Update drag feedback based on movement
   */
  private updateDragFeedback(
    _deltaX: number,
    mouseX: number,
    mouseY: number,
  ): void {
    // Update drag image position to follow cursor
    if (this.dragImage) {
      this.dragImageProvider.updateDragImagePosition(this.dragImage, mouseX, mouseY);
    }

    // Check which drop zone we're over using the service
    const newDropZone = this.dropZoneDetector.getDropZoneAtPosition(mouseX, mouseY);

    // Update visual feedback if drop zone changed
    if (newDropZone !== this.currentDropZone) {
      this.dropZoneDetector.updateDropZoneHighlight(this.currentDropZone, newDropZone);
      this.currentDropZone = newDropZone;
    }
  }



  /**
   * End the drag operation and determine new position
   */
  private endDrag(): void {
    this.isDragging = false;

    // Clean up drag image using the service
    if (this.dragImage) {
      this.dragImageProvider.cleanupDragImage(this.dragImage);
      this.dragImage = null;
    }

    // Reset visual feedback using the service
    const dragTarget = this.dragTargetProvider.getDragTarget(this.elementRef.nativeElement);
    this.dragTargetProvider.applyDragFeedback(dragTarget, false);

    // Only change position if dropped on a valid drop zone and it's a valid position
    if (this.currentDropZone &&
        this.currentDropZone !== this.toolbarPosition &&
        (this.currentDropZone === 'left' || this.currentDropZone === 'right')) {
      this.positionChange.emit(this.currentDropZone as 'left' | 'right');
    }

    // Reset drop zone tracking
    this.currentDropZone = null;

    // Notify parent that dragging ended
    this.dragStateChange.emit(false);
  }

  /**
   * Cleanup on destroy
   */
  ngOnDestroy(): void {
    // Clean up any remaining drag image using the service
    if (this.dragImage) {
      this.dragImageProvider.cleanupDragImage(this.dragImage);
    }
  }
}
