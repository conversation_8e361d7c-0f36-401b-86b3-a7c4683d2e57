import { Injectable, Component } from '@angular/core';
import { 
  DropZoneDetector, 
  DragTargetProvider, 
  <PERSON><PERSON><PERSON>mageProvider, 
  DragConfig,
  DROP_ZONE_DETECTOR,
  DRAG_TARGET_PROVIDER,
  DRAG_IMAGE_PROVIDER,
  DRAG_CONFIG
} from '../interface/drag-drop.interface';

/**
 * Example of a custom drop zone detector that uses different selectors
 */
@Injectable()
export class CustomDropZoneService implements DropZoneDetector {
  
  private readonly customConfig = {
    leftZone: '.my-custom-left-zone',
    rightZone: '.my-custom-right-zone',
    activeClass: 'active',
    highlightClass: 'highlighted'
  };

  getDropZoneAtPosition(x: number, y: number): string | null {
    // Custom implementation using different selectors
    const leftZone = document.querySelector(this.customConfig.leftZone) as HTMLElement;
    const rightZone = document.querySelector(this.customConfig.rightZone) as HTMLElement;

    if (leftZone && this.isDropZoneVisible('left')) {
      const rect = leftZone.getBoundingClientRect();
      if (this.isPointInRect(x, y, rect)) {
        return 'left';
      }
    }

    if (rightZone && this.isDropZoneVisible('right')) {
      const rect = rightZone.getBoundingClientRect();
      if (this.isPointInRect(x, y, rect)) {
        return 'right';
      }
    }

    return null;
  }

  updateDropZoneHighlight(oldZone: string | null, newZone: string | null): void {
    // Custom highlighting logic
    if (oldZone) {
      const oldElement = this.getZoneElement(oldZone);
      if (oldElement) {
        oldElement.classList.remove(this.customConfig.highlightClass);
      }
    }

    if (newZone) {
      const newElement = this.getZoneElement(newZone);
      if (newElement) {
        newElement.classList.add(this.customConfig.highlightClass);
      }
    }
  }

  isDropZoneVisible(zoneId: string): boolean {
    const element = this.getZoneElement(zoneId);
    return element ? element.classList.contains(this.customConfig.activeClass) : false;
  }

  private getZoneElement(zoneId: string): HTMLElement | null {
    const selector = zoneId === 'left' ? this.customConfig.leftZone : this.customConfig.rightZone;
    return document.querySelector(selector) as HTMLElement;
  }

  private isPointInRect(x: number, y: number, rect: DOMRect): boolean {
    return x >= rect.left && x <= rect.right && y >= rect.top && y <= rect.bottom;
  }
}

/**
 * Example of a custom drag target provider
 */
@Injectable()
export class CustomDragTargetService implements DragTargetProvider {
  
  getDragTarget(sourceElement: HTMLElement): HTMLElement | null {
    // Custom logic to find drag target - maybe look for a different container
    return sourceElement.closest('.my-custom-container') as HTMLElement;
  }

  applyDragFeedback(target: HTMLElement | null, isDragging: boolean): void {
    if (!target) return;

    if (isDragging) {
      // Custom dragging styles
      target.style.opacity = '0.6';
      target.style.transform = 'rotate(2deg) scale(0.98)';
      target.classList.add('my-custom-dragging');
    } else {
      // Reset styles
      target.style.opacity = '1';
      target.style.transform = 'none';
      target.classList.remove('my-custom-dragging');
    }
  }
}

/**
 * Example component using custom implementations
 */
@Component({
  selector: 'custom-toolbar',
  template: `
    <div class="my-custom-container">
      <div class="toolbar-drag-handle"
           toolbarDrag
           [toolbarDragEnabled]="true"
           [toolbarPosition]="position"
           (dragStateChange)="onDragStateChange($event)"
           (positionChange)="onPositionChange($event)"
           (mousedown)="toolbarDragDirective.onMouseDown($event)"
           #toolbarDragDirective="toolbarDrag">
        Custom Drag Handle
      </div>
    </div>
  `,
  providers: [
    CustomDropZoneService,
    CustomDragTargetService,
    // Use custom implementations
    { provide: DROP_ZONE_DETECTOR, useClass: CustomDropZoneService },
    { provide: DRAG_TARGET_PROVIDER, useClass: CustomDragTargetService },
    // Use default drag image provider
    { provide: DRAG_IMAGE_PROVIDER, useClass: CustomDragTargetService }, // This would be the default service
    // Custom configuration
    { 
      provide: DRAG_CONFIG, 
      useValue: {
        dragThreshold: 5, // More sensitive
        createDragImage: true,
        dragImageStyles: {
          border: '3px solid red', // Custom styling
          borderRadius: '12px'
        }
      } as DragConfig
    }
  ]
})
export class CustomToolbarComponent {
  position: 'left' | 'right' = 'right';

  onDragStateChange(isDragging: boolean): void {
    console.log('Custom drag state:', isDragging);
  }

  onPositionChange(newPosition: 'left' | 'right'): void {
    this.position = newPosition;
    console.log('Custom position change:', newPosition);
  }
}
