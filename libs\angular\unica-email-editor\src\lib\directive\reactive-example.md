# Reactive Drag Implementation Example

## RxJS Stream Flow

The directive uses a reactive approach with the following stream flow:

```typescript
// 1. Mouse down triggers the drag start subject
onMouseDown(event: MouseEvent): void {
  this.dragStart$.next(event);
}

// 2. Setup reactive streams in constructor
private setupDragStreams(): void {
  const mouseDown$ = this.dragStart$.pipe(
    filter(() => this.toolbarDragEnabled),
    tap(event => {
      event.preventDefault();
      event.stopPropagation();
    })
  );

  const mouseMove$ = fromEvent<MouseEvent>(document, 'mousemove');
  const mouseUp$ = fromEvent<MouseEvent>(document, 'mouseup');

  // 3. Create drag sequence: mousedown -> mousemove until mouseup
  const drag$ = mouseDown$.pipe(
    switchMap(startEvent => {
      const startX = startEvent.clientX;
      let isDragging = false;

      return mouseMove$.pipe(
        map(moveEvent => ({
          startX,
          currentX: moveEvent.clientX,
          currentY: moveEvent.clientY,
          deltaX: moveEvent.clientX - startX,
          isDragging,
          moveEvent
        })),
        tap(dragData => {
          // Start dragging if threshold exceeded
          if (!dragData.isDragging && Math.abs(dragData.deltaX) > this.dragThreshold) {
            isDragging = true;
            dragData.isDragging = true;
            this.startDrag();
          }
        }),
        filter(dragData => dragData.isDragging),
        tap(dragData => this.updateDragFeedback(dragData.currentX, dragData.currentY)),
        takeUntil(mouseUp$.pipe(tap(() => this.endDrag())))
      );
    }),
    takeUntil(this.destroy$)
  );

  drag$.subscribe();
}
```

## Renderer2 Usage Examples

### Safe Style Manipulation
```typescript
// Instead of: element.style.opacity = '0.4'
this.renderer.setStyle(target, 'opacity', '0.4');

// Instead of: element.style.transform = 'scale(0.95)'
this.renderer.setStyle(target, 'transform', 'scale(0.95)');
```

### Safe Class Management
```typescript
// Instead of: element.classList.add('hover')
this.renderer.addClass(element, this.hoverClass);

// Instead of: element.classList.remove('hover')
this.renderer.removeClass(element, this.hoverClass);
```

### Safe DOM Manipulation
```typescript
// Instead of: document.body.appendChild(dragImage)
this.renderer.appendChild(document.body, this.dragImage);

// Instead of: document.body.removeChild(dragImage)
this.renderer.removeChild(document.body, this.dragImage);
```

## NgZone Integration

Events are emitted within NgZone to ensure proper change detection:

```typescript
private endDrag(): void {
  // ... cleanup logic

  // Emit events within NgZone for change detection
  this.ngZone.run(() => {
    this.dragStateChange.emit(false);
    if (this.currentDropZone) {
      this.positionChange.emit(this.currentDropZone);
    }
  });
}
```

## Memory Management

Proper cleanup prevents memory leaks:

```typescript
ngOnDestroy(): void {
  // Complete RxJS streams
  this.destroy$.next();
  this.destroy$.complete();
  this.dragStart$.complete();

  // Clean up DOM elements
  if (this.dragImage && document.body.contains(this.dragImage)) {
    this.renderer.removeChild(document.body, this.dragImage);
  }
}
```

## Benefits of This Approach

1. **Declarative**: The drag logic is expressed as data transformations through RxJS operators
2. **Memory Safe**: Automatic cleanup with `takeUntil(this.destroy$)`
3. **Composable**: Easy to add new features by adding operators to the stream
4. **Testable**: Each operator can be tested independently
5. **SSR Compatible**: Renderer2 ensures server-side rendering works
6. **Performance**: NgZone integration ensures optimal change detection
